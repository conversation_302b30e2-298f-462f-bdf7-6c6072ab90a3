import React from 'react';
import { Box, styled } from '@mui/material';
import { useThemeMode } from '../contexts/ThemeContext';

const GlobalWrapper = styled(Box)(({ theme, isDark }) => ({
  minHeight: '100vh',
  width: '100%',
  background: isDark ? 
    `linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)` : 
    '#f5f9ff',
  transition: 'all 0.3s ease',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: isDark ? 
      `linear-gradient(135deg,
        rgba(15, 23, 42, 0.97) 0%,
        rgba(30, 41, 59, 0.95) 25%,
        rgba(51, 65, 85, 0.93) 50%,
        rgba(71, 85, 105, 0.95) 75%,
        rgba(100, 116, 139, 0.97) 100%
      )` : 
      '#f5f9ff',
    zIndex: -1,
    pointerEvents: 'none',
  }
}));

const SectionWrapper = styled(Box)(({ theme, isDark, sectionType }) => {
  const getSectionBackground = () => {
    if (!isDark) {
      switch (sectionType) {
        case 'hero':
          return '#f5f9ff';
        case 'stats':
          return '#ffffff';
        case 'features':
          return '#f8fafc';
        case 'about':
          return '#ffffff';
        case 'services':
          return '#f8fafc';
        case 'team':
          return '#ffffff';
        case 'customers':
          return '#f8fafc';
        case 'footer':
          return '#1e293b';
        default:
          return '#ffffff';
      }
    } else {
      // Dark mode - all sections use the same gradient background
      return `linear-gradient(135deg,
        rgba(15, 23, 42, 0.97) 0%,
        rgba(30, 41, 59, 0.95) 25%,
        rgba(51, 65, 85, 0.93) 50%,
        rgba(71, 85, 105, 0.95) 75%,
        rgba(100, 116, 139, 0.97) 100%
      )`;
    }
  };

  return {
    background: getSectionBackground(),
    transition: 'all 0.3s ease',
    position: 'relative',
    width: '100%',
    '&::before': isDark ? {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.05) 0%, transparent 50%)
      `,
      zIndex: 0,
      pointerEvents: 'none',
    } : {},
    '& > *': {
      position: 'relative',
      zIndex: 1,
    }
  };
});

const ThemeWrapper = ({ children, sectionType = 'default' }) => {
  const { isDarkMode } = useThemeMode();

  return (
    <SectionWrapper isDark={isDarkMode} sectionType={sectionType}>
      {children}
    </SectionWrapper>
  );
};

export { ThemeWrapper, GlobalWrapper };
export default ThemeWrapper;
