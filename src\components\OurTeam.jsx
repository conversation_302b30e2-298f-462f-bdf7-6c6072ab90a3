import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, styled, useTheme, useMediaQuery, Container } from '@mui/material';
import { motion, AnimatePresence, useInView } from 'framer-motion';
import { FaLinkedinIn, FaTwitter, FaInstagram, FaFacebookF } from 'react-icons/fa';
import { AutoAwesome, TrendingUp, People } from '@mui/icons-material';
import { useThemeMode } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'CEO & Founder',
    tagline: 'Leadership • Vision • 10+ Years',
    description: 'Drives strategic growth with over a decade of leadership experience in consulting.',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/janedoe',
    twitter: 'https://twitter.com/janedoe',
    instagram: 'https://www.instagram.com/janedoe/',
    facebook: 'https://www.facebook.com/janedoe',
  },
  {
    name: '<PERSON>',
    role: 'Lead Developer',
    tagline: 'Innovation • Coding • 8+ Years',
    description: 'Expert in building scalable tech solutions with a focus on modern development practices.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/johnsmith',
    twitter: 'https://twitter.com/johnsmith',
    instagram: 'https://www.instagram.com/johnsmith/',
    facebook: 'https://www.facebook.com/johnsmith',
  },
  {
    name: 'Emily Johnson',
    role: 'Marketing Director',
    tagline: 'Strategy • Branding • 6+ Years',
    description: 'Leads creative campaigns to elevate brand presence across multiple platforms.',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/emilyjohnson',
    twitter: 'https://twitter.com/emilyjohnson',
    instagram: 'https://www.instagram.com/emilyjohnson/',
    facebook: 'https://www.facebook.com/emilyjohnson',
  },
  {
    name: 'Michael Brown',
    role: 'Design Lead',
    tagline: 'Creativity • UI/UX • 5+ Years',
    description: 'Crafts stunning user interfaces with a passion for user-centered design.',
    image: 'https://images.unsplash.com/photo-1500048993953-d23a436266cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/michaelbrown',
    twitter: 'https://twitter.com/michaelbrown',
    instagram: 'https://www.instagram.com/michaelbrown/',
    facebook: 'https://www.facebook.com/michaelbrown',
  },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: '80px',
  padding: '80px 0',
  background: `
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  overflow: 'hidden',
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('md')]: {
    padding: '60px 0',
    marginTop: '60px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 0',
    marginTop: '40px',
  },
}));

const Grid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: '32px',
  marginTop: '60px',
  padding: '0 20px',
  gridTemplateColumns: 'repeat(4, 1fr)', // Fixed 4 columns for desktop
  width: '100%',
  maxWidth: '1600px',
  marginLeft: 'auto',
  marginRight: 'auto',
  position: 'relative',
  justifyItems: 'center',
  [theme.breakpoints.up('xl')]: {
    gridTemplateColumns: 'repeat(4, 1fr)', // Keep 4 columns on extra large screens
    gap: '40px',
    maxWidth: '1800px',
  },
  [theme.breakpoints.down('lg')]: {
    gridTemplateColumns: 'repeat(2, 1fr)', // 2 columns for large tablets
    gap: '28px',
  },
  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(2, 1fr)', // 2 columns for tablets
    gap: '24px',
    padding: '0 16px',
  },
  [theme.breakpoints.down('sm')]: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '20px',
    padding: '0 12px',
    overflow: 'visible',
  },
}));

const CardWrapper = styled(Box)(({ theme }) => ({
  perspective: '2000px',
  width: '100%',
  maxWidth: '380px',
  height: '600px',
  margin: '0 auto',
  position: 'relative',
  transformStyle: 'preserve-3d',
  [theme.breakpoints.up('xl')]: {
    maxWidth: '400px',
    height: '620px',
  },
  [theme.breakpoints.down('lg')]: {
    maxWidth: '360px',
    height: '580px',
  },
  [theme.breakpoints.down('md')]: {
    maxWidth: '340px',
    height: '560px',
  },
  [theme.breakpoints.down('sm')]: {
    maxWidth: '320px',
    height: '540px',
    margin: '0 auto',
  },
}));

const Card = styled(motion.div)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '100%',
  borderRadius: '24px',
  background: `
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.05) 100%
    )
  `,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(20px)',
  WebkitBackdropFilter: 'blur(20px)',
  boxShadow: `
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  willChange: 'transform, box-shadow',
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: `
      0 35px 70px -12px rgba(0, 0, 0, 0.35),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 50px rgba(59, 130, 246, 0.3)
    `,
    '& .card-content': {
      transform: 'translateY(-5px)',
    },
  },
  '&:active': {
    transform: 'translateY(-4px) scale(0.98)',
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: '20px',
  },
}));

const CardContent = styled(motion.div)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  alignItems: 'center',
  textAlign: 'center',
  padding: '32px 24px',
  color: '#f8fafc',
  transition: 'transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  zIndex: 1,
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '28px 20px',
  },
}));

const Image = styled('img')(({ theme }) => ({
  width: '100%',
  height: '220px',
  objectFit: 'cover',
  borderRadius: '16px',
  marginBottom: '20px',
  objectPosition: 'center',
  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',
  border: '2px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
  '&:hover': {
    transform: 'scale(1.02)',
    filter: 'brightness(1.2) contrast(1.2) saturate(1.3)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.18)',
  },
  [theme.breakpoints.down('md')]: {
    height: '200px',
  },
  [theme.breakpoints.down('sm')]: {
    height: '180px',
    borderRadius: '12px',
  },
}));

const SocialIcons = styled(motion.div)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  gap: '16px',
  padding: '20px 0',
  width: '100%',
  boxSizing: 'border-box',
  '& a': {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '44px',
    height: '44px',
    borderRadius: '50%',
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    color: '#cbd5e1',
    transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    textDecoration: 'none',
    '&:hover': {
      transform: 'translateY(-2px) scale(1.1)',
      background: 'rgba(59, 130, 246, 0.2)',
      border: '1px solid rgba(59, 130, 246, 0.4)',
      color: '#3b82f6',
      boxShadow: '0 8px 25px rgba(59, 130, 246, 0.3)',
    },
    '&:active': {
      transform: 'translateY(0) scale(0.95)',
    },
  },
  [theme.breakpoints.down('sm')]: {
    gap: '12px',
    '& a': {
      width: '40px',
      height: '40px',
    },
  },
}));

const OurTeam = () => {
  const { isDarkMode } = useThemeMode();
  const { t } = useLanguage();

  const [visibleIndex, setVisibleIndex] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const sectionRef = useRef(null);
  const gridRef = useRef(null);
  const sectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isInView = useInView(gridRef, { once: false, amount: 0.2 });

  useEffect(() => {
    if (!isMobile) return;
    const interval = setInterval(() => {
      setVisibleIndex((prev) => (prev + 1) % teamMembers.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [isMobile]);

  // Enhanced animation variants
  const textVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
        staggerChildren: 0.1
      }
    },
  };

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9,
      rotateX: 15,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275]
      },
    },
  };

  // Stagger animation for grid items
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const iconVariants = {
    initial: { opacity: 0.7, scale: 1 },
    animate: {
      opacity: 1,
      scale: 1.05,
      transition: {
        duration: 1.5,
        repeat: Infinity,
        repeatType: 'reverse',
        ease: 'easeInOut',
      },
    },
  };

  return (
    <Section id="our-team" ref={sectionRef}>
      <Container maxWidth="xl">
        <motion.div
          initial="hidden"
          animate={sectionInView ? "visible" : "hidden"}
          variants={textVariants}
          style={{ textAlign: 'center', marginBottom: '40px' }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={sectionInView ? { opacity: 1, scale: 1, y: 0 } : {}}
            transition={{ duration: 1, ease: [0.175, 0.885, 0.32, 1.275] }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <AutoAwesome sx={{ fontSize: '2rem', color: '#3b82f6', mr: 2 }} />
              <Typography
                variant="h2"
                sx={{
                  fontWeight: '900',
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                  fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                  letterSpacing: '-0.02em',
                }}
              >
                {t('team.title')}
              </Typography>
              <People sx={{ fontSize: '2rem', color: '#10b981', ml: 2 }} />
            </Box>
            <Typography
              variant="h5"
              sx={{
                maxWidth: 800,
                mx: 'auto',
                color: '#cbd5e1',
                fontWeight: '400',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                opacity: 0.9
              }}
            >
              {t('team.subtitle')}
            </Typography>
          </motion.div>
        </motion.div>

      {isMobile ? (
        <Box sx={{ mt: 4, width: '100%' }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={visibleIndex}
              initial={{ x: 200, opacity: 0, rotate: 15 }}
              animate={{ x: 0, opacity: 1, rotate: 0 }}
              exit={{ x: -200, opacity: 0, rotate: -15 }}
              transition={{ duration: 0.8, ease: 'easeInOut' }}
            >
              <CardWrapper>
                <Card>
                  <CardContent className="card-content">
                    <Image src={teamMembers[visibleIndex].image} alt={teamMembers[visibleIndex].name} />
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, justifyContent: 'center' }}>
                      <TrendingUp sx={{ fontSize: '1rem', color: '#3b82f6', mr: 1 }} />
                      <Typography variant="caption" sx={{
                        opacity: 0.8,
                        fontSize: '0.85rem',
                        fontWeight: '600',
                        color: '#64748b',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        {teamMembers[visibleIndex].tagline}
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{
                      fontWeight: '800',
                      mb: 2,
                      color: '#f8fafc',
                      fontSize: { xs: '1.3rem', md: '1.5rem' },
                      lineHeight: 1.2
                    }}>
                      {teamMembers[visibleIndex].name}
                    </Typography>
                    <Typography variant="body1" sx={{
                      fontSize: { xs: '0.85rem', md: '0.95rem' },
                      lineHeight: 1.5,
                      color: '#cbd5e1',
                      mb: 2,
                      textAlign: 'center'
                    }}>
                      {teamMembers[visibleIndex].description}
                    </Typography>
                    <SocialIcons className="social-icons" variants={iconVariants} initial="initial" animate="animate">
                      <a href={teamMembers[visibleIndex].linkedin} target="_blank" rel="noopener noreferrer">
                        <FaLinkedinIn size={20} />
                      </a>
                      <a href={teamMembers[visibleIndex].twitter} target="_blank" rel="noopener noreferrer">
                        <FaTwitter size={20} />
                      </a>
                      <a href={teamMembers[visibleIndex].instagram} target="_blank" rel="noopener noreferrer">
                        <FaInstagram size={20} />
                      </a>
                      <a href={teamMembers[visibleIndex].facebook} target="_blank" rel="noopener noreferrer">
                        <FaFacebookF size={20} />
                      </a>
                    </SocialIcons>
                  </CardContent>
                </Card>
              </CardWrapper>
            </motion.div>
          </AnimatePresence>
        </Box>
        ) : (
          <motion.div
            ref={gridRef}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            <Grid>
              {teamMembers.map((member, index) => (
                <motion.div
                  key={member.name}
                  custom={index}
                  variants={cardVariants}
                  style={{
                    position: 'relative',
                    width: '100%',
                    willChange: 'transform, opacity'
                  }}
                >
              <CardWrapper>
                <Card>
                  <CardContent className="card-content">
                    <Image src={member.image} alt={member.name} />
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, justifyContent: 'center' }}>
                      <TrendingUp sx={{ fontSize: '1rem', color: '#3b82f6', mr: 1 }} />
                      <Typography variant="caption" sx={{
                        opacity: 0.8,
                        fontSize: '0.85rem',
                        fontWeight: '600',
                        color: '#64748b',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        {member.tagline}
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{
                      fontWeight: '800',
                      mb: 2,
                      color: '#f8fafc',
                      fontSize: { xs: '1.3rem', md: '1.5rem' },
                      lineHeight: 1.2
                    }}>
                      {member.name}
                    </Typography>
                    <Typography variant="body1" sx={{
                      fontSize: { xs: '0.85rem', md: '0.95rem' },
                      lineHeight: 1.5,
                      color: '#cbd5e1',
                      mb: 2,
                      textAlign: 'center'
                    }}>
                      {member.description}
                    </Typography>
                    <SocialIcons className="social-icons" variants={iconVariants} initial="initial" animate="animate">
                      <a href={member.linkedin} target="_blank" rel="noopener noreferrer">
                        <FaLinkedinIn size={20} />
                      </a>
                      <a href={member.twitter} target="_blank" rel="noopener noreferrer">
                        <FaTwitter size={20} />
                      </a>
                      <a href={member.instagram} target="_blank" rel="noopener noreferrer">
                        <FaInstagram size={20} />
                      </a>
                      <a href={member.facebook} target="_blank" rel="noopener noreferrer">
                        <FaFacebookF size={20} />
                      </a>
                    </SocialIcons>
                  </CardContent>
                </Card>
              </CardWrapper>
            </motion.div>
                ))}
              </Grid>
            </motion.div>
          )}
        </Container>
      </Section>
    );
  };

export default OurTeam;