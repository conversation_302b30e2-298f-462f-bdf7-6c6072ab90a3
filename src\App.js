import React, { useEffect, Suspense } from 'react';
import { ThemeContextProvider } from './contexts/ThemeContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { GlobalWrapper } from './components/ThemeWrapper';
import ThemeWrapper from './components/ThemeWrapper';
import { preloadCriticalResources, usePerformanceMonitor } from './utils/performance';
import { Box, CircularProgress } from '@mui/material';

// Critical components (above the fold)
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import Stats from './components/Stats';

// Lazy load non-critical components
const Features = React.lazy(() => import('./components/Features'));
const AboutUs = React.lazy(() => import('./components/AboutUs'));
const Services = React.lazy(() => import('./components/OurServices'));
const OurTeam = React.lazy(() => import('./components/OurTeam'));
const Customers = React.lazy(() => import('./components/Customers'));
const Footer = React.lazy(() => import('./components/Footer'));

// Loading component
const LoadingSpinner = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    minHeight="200px"
  >
    <CircularProgress />
  </Box>
);

function App() {
  // Performance monitoring
  usePerformanceMonitor();

  // Preload critical resources
  useEffect(() => {
    preloadCriticalResources();
  }, []);

  return (
    <ThemeContextProvider>
      <LanguageProvider>
        <GlobalWrapper>
          <Navbar />

          <ThemeWrapper sectionType="hero">
            <div id="home">
              <Hero />
            </div>
          </ThemeWrapper>

          <ThemeWrapper sectionType="stats">
            <div id="stats">
              <Stats />
            </div>
          </ThemeWrapper>

          <Suspense fallback={<LoadingSpinner />}>
            <ThemeWrapper sectionType="features">
              <div id="features">
                <Features />
              </div>
            </ThemeWrapper>

            <ThemeWrapper sectionType="about">
              <div id="about">
                <AboutUs />
              </div>
            </ThemeWrapper>

            <ThemeWrapper sectionType="services">
              <div id="services">
                <Services />
              </div>
            </ThemeWrapper>

            <ThemeWrapper sectionType="team">
              <div id="team">
                <OurTeam />
              </div>
            </ThemeWrapper>

            <ThemeWrapper sectionType="customers">
              <div id="customers">
                <Customers />
              </div>
            </ThemeWrapper>

            <ThemeWrapper sectionType="footer">
              <div id="contact">
                <Footer />
              </div>
            </ThemeWrapper>
          </Suspense>

        </GlobalWrapper>
      </LanguageProvider>
    </ThemeContextProvider>
  );
}

export default App;
