import React from 'react';
import { ThemeContextProvider } from './contexts/ThemeContext';
import { LanguageProvider } from './contexts/LanguageContext';
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import Stats from './components/Stats';
import Features from './components/Features';
import AboutUs from './components/AboutUs';
import Services from './components/OurServices';
import Footer from './components/Footer';
import OurTeam from './components/OurTeam';
import Customers from './components/Customers';

function App() {
  return (
    <ThemeContextProvider>
      <LanguageProvider>
        <div>
          <Navbar />

          <div id="home">
            <Hero />
          </div>

          <div id="stats">
            <Stats />
          </div>

          <div id="features">
            <Features />
          </div>

          <div id="about">
            <AboutUs />
          </div>

          <div id="services">
            <Services />
          </div>

          <div id="team">
            <OurTeam />
          </div>

          <div id="customers">
            <Customers />
          </div>

          <div id="contact">
            <Footer />
          </div>

        </div>
      </LanguageProvider>
    </ThemeContextProvider>
  );
}

export default App;
