import React from "react";
import { Box, Typography, Button } from "@mui/material";
import { styled } from "@mui/system";
import ladyImage from "../assets/images/blog-1.jpg";
import TRA from "../assets/logos/tra-logo.png";
import BOT from "../assets/logos/bot-logo.png";
import MINISTRY from "../assets/logos/ministry-logo.png";
import SSRA from "../assets/logos/ssra.png";

// Hero Wrapper
const HeroWrapper = styled(Box)(({ theme }) => ({
  position: "relative",
  display: "flex",
  width: "100%",
  minHeight: "100vh",
  alignItems: "center",
  backgroundColor: "#f5f9ff",
  overflow: "hidden",
  marginTop: "76px",
  [theme.breakpoints.down("md")]: {
    flexDirection: "column",
    minHeight: "60vh",
  },
}));

// Background Image
const BackgroundImage = styled("img")(({ theme }) => ({
  position: "absolute",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  objectFit: "cover",
  zIndex: 1,
  [theme.breakpoints.down("md")]: {
    height: "60vh",
  },
}));

// Content Box
const ContentBox = styled(Box)(({ theme }) => ({
  position: "relative",
  zIndex: 2,
  width: "50%",
  backgroundColor: "rgba(255, 255, 255, 0.75)",
  borderRadius: "24px",
  padding: "48px",
  marginLeft: "32px",
  boxShadow: "0 12px 28px rgba(0,0,0,0.12)",
  backdropFilter: "blur(10px)",
  fontFamily: "'Roboto', sans-serif",
  [theme.breakpoints.down("md")]: {
    width: "90%",
    marginLeft: 0,
    marginTop: "20px",
    padding: "32px",
  },
}));

// Logos
const LogoContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexWrap: "wrap",
  justifyContent: "center",
  gap: "40px",
  marginTop: "40px",
}));

const StyledLogo = styled("img")(({ theme }) => ({
  height: "90px",
  objectFit: "contain",
  [theme.breakpoints.down("md")]: { height: "60px" },
  [theme.breakpoints.down("sm")]: { height: "40px" },
}));

// Component
const Hero = () => {
  return (
    <HeroWrapper>
      <BackgroundImage src={ladyImage} alt="Hero Background" />
      <ContentBox>
        <Typography
          variant="h3"
          sx={{
            fontWeight: 800,
            color: "#1a237e",
            fontFamily: "'Montserrat', sans-serif",
            fontSize: {
              xs: "26px",
              sm: "34px",
              md: "42px",
              lg: "48px",
              xl: "54px",
            },
            mb: 1,
          }}
        >
          Welcome to Nelainey
        </Typography>

        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: "#2a3b8f",
            fontFamily: "'Roboto', sans-serif",
            fontSize: {
              xs: "16px",
              sm: "20px",
              md: "22px",
              lg: "24px",
              xl: "26px",
            },
            mb: 2,
          }}
        >
          Where Expertise Meets Excellence
        </Typography>

        <Typography
          variant="body1"
          sx={{
            fontSize: {
              xs: "14px",
              sm: "16px",
              md: "18px",
              lg: "20px",
            },
            lineHeight: 1.6,
            color: "#444",
            maxWidth: "600px",
            fontFamily: "'Roboto', sans-serif",
            mb: 4,
          }}
        >
          At Nelainey Consulting, we provide strategic, technology-driven
          solutions in Accounting, Auditing, Tax, HR, and Business Advisory.
          Our approach blends regional expertise and global standards to help
          you navigate complexity and achieve sustainable growth.
        </Typography>

        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
          <Button
            variant="contained"
            sx={{
              backgroundColor: "#1a237e",
              color: "#fff",
              fontSize: "14px",
              px: 3,
              py: 1.5,
              fontWeight: 600,
              borderRadius: "12px",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#0d1136",
              },
            }}
          >
            Get Started
          </Button>
          <Button
            variant="outlined"
            sx={{
              fontSize: "14px",
              px: 3,
              py: 1.5,
              fontWeight: 500,
              borderColor: "#1a237e",
              color: "#1a237e",
              borderRadius: "12px",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#e8eaf6",
                borderColor: "#0d1136",
              },
            }}
          >
            Contact Us
          </Button>
        </Box>

        <LogoContainer>
          <StyledLogo src={TRA} alt="TRA" />
          <StyledLogo src={BOT} alt="BOT" />
          <StyledLogo src={MINISTRY} alt="Ministry" />
          <StyledLogo src={SSRA} alt="SSRA" />
        </LogoContainer>
      </ContentBox>
    </HeroWrapper>
  );
};

export default Hero;
