import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  styled,
  IconButton,
  TextField,
  Button,
  Grid,
  Chip,
  Avatar,
  Card,
  CardContent,
  Link,
  Tooltip,
  useTheme,
} from "@mui/material";
import { useThemeMode } from "../contexts/ThemeContext";
import { useLanguage } from "../contexts/LanguageContext";
import { motion, AnimatePresence } from "framer-motion";
import FacebookIcon from "@mui/icons-material/Facebook";
import TwitterIcon from "@mui/icons-material/Twitter";
import InstagramIcon from "@mui/icons-material/Instagram";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import YouTubeIcon from "@mui/icons-material/YouTube";
import CloseIcon from "@mui/icons-material/Close";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import BusinessIcon from "@mui/icons-material/Business";
import SecurityIcon from "@mui/icons-material/Security";
import VerifiedIcon from "@mui/icons-material/Verified";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import PublicIcon from "@mui/icons-material/Public";
import SendIcon from "@mui/icons-material/Send";
import StarIcon from "@mui/icons-material/Star";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";

const FooterSection = styled(Box)(({ theme }) => ({
  background: "linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)",
  color: "#f8fafc",
  padding: "60px 0 0",
  fontFamily: "'Inter', sans-serif",
  userSelect: "none",
  width: "100%",
  boxSizing: "border-box",
  position: "relative",
  overflow: "hidden",
  marginTop: "40px",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background:
      "radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.1) 0%, transparent 50%)",
    pointerEvents: "none",
  },
  [theme.breakpoints.up("xl")]: {
    padding: "80px 0 0",
  },
  [theme.breakpoints.down("md")]: {
    padding: "40px 0 0",
    marginTop: "20px",
  },
}));

const Container = styled(Box)(({ theme }) => ({
  maxWidth: "1400px",
  margin: "0 auto",
  padding: "0 20px",
  position: "relative",
  zIndex: 1,
  [theme.breakpoints.up("xl")]: {
    maxWidth: "1600px",
    padding: "0 40px",
  },
  [theme.breakpoints.up("lg")]: {
    padding: "0 32px",
  },
  [theme.breakpoints.down("sm")]: {
    padding: "0 16px",
  },
}));

const MainFooterContent = styled(Grid)(({ theme }) => ({
  marginBottom: "60px",
  width: "100%",
  margin: "0 auto 60px auto",
  spacing: { xs: 3, sm: 4, md: 5, lg: 6, xl: 6 },
  [theme.breakpoints.up("xl")]: {
    marginBottom: "80px",
  },
  [theme.breakpoints.down("md")]: {
    marginBottom: "40px",
  },
}));

const FooterCard = styled(motion.div)(({ theme }) => ({
  background: "rgba(255, 255, 255, 0.05)",
  backdropFilter: "blur(20px)",
  WebkitBackdropFilter: "blur(20px)",
  borderRadius: "20px",
  padding: "28px",
  border: "1px solid rgba(255, 255, 255, 0.1)",
  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
  width: "100%",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  position: "relative",
  overflow: "hidden",
  zIndex: 2,
  transition: "all 0.3s ease",
  "&:hover": {
    transform: "translateY(-6px)",
    boxShadow: "0 20px 48px rgba(0, 0, 0, 0.15)",
    border: "1px solid rgba(59, 130, 246, 0.4)",
  },
  [theme.breakpoints.up("xl")]: {
    padding: "36px",
    borderRadius: "24px",
  },
  [theme.breakpoints.up("lg")]: {
    padding: "32px",
  },
  [theme.breakpoints.down("md")]: {
    padding: "20px",
    borderRadius: "16px",
    marginBottom: "16px",
  },
  [theme.breakpoints.down("sm")]: {
    padding: "16px",
    borderRadius: "14px",
    marginBottom: "12px",
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: "1.5rem",
  marginBottom: "24px",
  color: "#ffffff",
  display: "flex",
  alignItems: "center",
  justifyContent: { xs: "center", md: "flex-start" },
  gap: "12px",
  textAlign: { xs: "center", md: "left" },
  "& .MuiSvgIcon-root": {
    fontSize: "1.8rem",
    color: "#3b82f6",
  },
  [theme.breakpoints.up("xl")]: {
    fontSize: "1.75rem",
    marginBottom: "32px",
    "& .MuiSvgIcon-root": {
      fontSize: "2rem",
    },
  },
  [theme.breakpoints.down("md")]: {
    fontSize: "1.2rem",
    marginBottom: "18px",
    justifyContent: "center",
    textAlign: "center",
    "& .MuiSvgIcon-root": {
      fontSize: "1.4rem",
    },
  },
  [theme.breakpoints.down("sm")]: {
    fontSize: "1.1rem",
    marginBottom: "16px",
    "& .MuiSvgIcon-root": {
      fontSize: "1.3rem",
    },
  },
}));

const LinkText = styled(Link)(({ theme }) => ({
  fontSize: "1rem",
  marginBottom: "12px",
  cursor: "pointer",
  color: "#cbd5e1",
  transition: "all 0.3s ease",
  textDecoration: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: { xs: "center", md: "flex-start" },
  gap: "8px",
  padding: "8px 0",
  borderRadius: "8px",
  textAlign: { xs: "center", md: "left" },
  "&:hover": {
    color: "#3b82f6",
    transform: "translateX(8px)",
    textDecoration: "none",
  },
  "& .MuiSvgIcon-root": {
    fontSize: "1.2rem",
    opacity: 0.7,
  },
  [theme.breakpoints.up("xl")]: {
    fontSize: "1.125rem",
    marginBottom: "16px",
    padding: "10px 0",
  },
  [theme.breakpoints.down("md")]: {
    fontSize: "0.9rem",
    marginBottom: "8px",
    padding: "6px 0",
    justifyContent: "center",
    textAlign: "center",
  },
  [theme.breakpoints.down("sm")]: {
    fontSize: "0.85rem",
    marginBottom: "10px", // Increased for better spacing
    padding: "6px 8px",
    gap: "6px", // Slightly reduced gap for compact look
    justifyContent: "center",
    textAlign: "center",
    "& .MuiSvgIcon-root": {
      fontSize: "1rem", // Smaller icon for mobile
      opacity: 0.8,
    },
    "&:hover": {
      transform: "none", // Disable transform on mobile for simplicity
      background: "rgba(59, 130, 246, 0.1)", // Subtle hover effect
    },
  },
}));

const ContactInfo = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: "12px",
  marginBottom: "16px",
  padding: "12px 16px",
  background: "rgba(255, 255, 255, 0.05)",
  borderRadius: "12px",
  border: "1px solid rgba(255, 255, 255, 0.1)",
  transition: "all 0.3s ease",
  "&:hover": {
    background: "rgba(59, 130, 246, 0.1)",
    border: "1px solid rgba(59, 130, 246, 0.3)",
  },
  [theme.breakpoints.up("xl")]: {
    padding: "16px 20px",
    marginBottom: "20px",
    borderRadius: "16px",
  },
  [theme.breakpoints.down("md")]: {
    padding: "10px 12px",
    marginBottom: "12px",
  },
}));

const SocialIconsBox = styled(Box)(({ theme }) => ({
  display: "flex",
  gap: "16px",
  marginTop: "24px",
  flexWrap: "wrap",
  justifyContent: "center",
  alignItems: "center",
  [theme.breakpoints.up("xl")]: {
    gap: "20px",
    marginTop: "32px",
  },
  [theme.breakpoints.down("md")]: {
    gap: "12px",
    marginTop: "20px",
    justifyContent: "center",
  },
  [theme.breakpoints.down("sm")]: {
    gap: "10px",
    marginTop: "16px",
  },
}));

const IconButtonStyled = styled(IconButton)(({ theme }) => ({
  color: "#cbd5e1",
  background:
    "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%)",
  border: "1px solid rgba(255, 255, 255, 0.1)",
  backdropFilter: "blur(10px)",
  transition: "all 0.3s ease",
  width: "56px",
  height: "56px",
  "&:hover": {
    color: "#ffffff",
    background: "linear-gradient(135deg, #3b82f6 0%, #a855f7 100%)",
    transform: "translateY(-4px) scale(1.1)",
    boxShadow: "0 12px 24px rgba(59, 130, 246, 0.4)",
    border: "1px solid rgba(59, 130, 246, 0.5)",
  },
  [theme.breakpoints.up("xl")]: {
    width: "64px",
    height: "64px",
    "& .MuiSvgIcon-root": {
      fontSize: "1.75rem",
    },
  },
  [theme.breakpoints.down("md")]: {
    width: "48px",
    height: "48px",
    "& .MuiSvgIcon-root": {
      fontSize: "1.25rem",
    },
  },
  [theme.breakpoints.down("sm")]: {
    width: "40px",
    height: "40px",
    "& .MuiSvgIcon-root": {
      fontSize: "1.1rem",
    },
  },
}));

const CertificationChip = styled(Chip)({
  background:
    "linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%)",
  color: "#ffffff",
  border: "1px solid rgba(34, 197, 94, 0.3)",
  margin: "4px",
  fontWeight: 600,
  "& .MuiChip-icon": {
    color: "#22c55e",
  },
});

const NewsletterCard = styled(Card)({
  background:
    "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%)",
  backdropFilter: "blur(20px)",
  border: "1px solid rgba(59, 130, 246, 0.2)",
  borderRadius: "20px",
  marginTop: "24px",
});

const SubFooter = styled(Box)(({ theme }) => ({
  borderTop: "1px solid rgba(255, 255, 255, 0.1)",
  padding: "32px 0",
  background: "rgba(0, 0, 0, 0.2)",
  backdropFilter: "blur(10px)",
  marginTop: "40px",
  position: "relative",
  width: "100%",
  boxSizing: "border-box",
  borderRadius: "16px", // Added rounded corners
  overflow: "hidden", // Ensures content respects rounded corners
  [theme.breakpoints.up("xl")]: {
    padding: "40px 0",
    marginTop: "60px",
    borderRadius: "20px", // Slightly larger radius for larger screens
  },
  [theme.breakpoints.down("md")]: {
    padding: "20px 0",
    marginTop: "30px",
    borderRadius: "14px", // Slightly smaller radius for medium screens
  },
  [theme.breakpoints.down("sm")]: {
    padding: "16px 0",
    borderRadius: "12px", // Smaller radius for small screens
  },
}));

const BackToTopButton = styled(IconButton)(({ theme }) => ({
  position: "fixed",
  bottom: "32px",
  right: "32px",
  background: "linear-gradient(135deg, #3b82f6 0%, #a855f7 100%)",
  color: "#ffffff",
  width: "56px",
  height: "56px",
  boxShadow: "0 8px 24px rgba(59, 130, 246, 0.4)",
  backdropFilter: "blur(10px)",
  border: "1px solid rgba(255, 255, 255, 0.2)",
  zIndex: 1000,
  transition: "all 0.3s ease",
  "&:hover": {
    background: "linear-gradient(135deg, #2563eb 0%, #9333ea 100%)",
    transform: "translateY(-4px) scale(1.1)",
    boxShadow: "0 12px 32px rgba(59, 130, 246, 0.6)",
  },
  [theme.breakpoints.down("md")]: {
    bottom: "24px",
    right: "24px",
    width: "48px",
    height: "48px",
  },
}));

const StatsGrid = styled(Grid)(({ theme }) => ({
  marginBottom: "50px",
  width: "100%",
  [theme.breakpoints.up("xl")]: {
    marginBottom: "70px",
  },
  [theme.breakpoints.down("md")]: {
    marginBottom: "40px",
  },
}));

const StatCard = styled(motion.div)(({ theme }) => ({
  background:
    "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%)",
  backdropFilter: "blur(20px)",
  borderRadius: "18px",
  padding: "24px 20px",
  textAlign: "center",
  border: "1px solid rgba(255, 255, 255, 0.1)",
  transition: "all 0.3s ease",
  height: "140px",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  margin: "0 auto",
  width: "100%",
  maxWidth: "280px",
  "&:hover": {
    transform: "translateY(-4px)",
    boxShadow: "0 16px 32px rgba(59, 130, 246, 0.2)",
    border: "1px solid rgba(59, 130, 246, 0.4)",
  },
  [theme.breakpoints.up("xl")]: {
    padding: "32px 24px",
    borderRadius: "20px",
    height: "160px",
    maxWidth: "320px",
  },
  [theme.breakpoints.up("lg")]: {
    height: "150px",
    maxWidth: "300px",
  },
  [theme.breakpoints.down("md")]: {
    padding: "20px 16px",
    borderRadius: "16px",
    height: "130px",
    maxWidth: "250px",
  },
  [theme.breakpoints.down("sm")]: {
    padding: "18px 14px",
    borderRadius: "14px",
    height: "120px",
    maxWidth: "200px",
  },
}));

const Overlay = styled(motion.div)({
  position: "fixed",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(0, 0, 0, 0.7)",
  backdropFilter: "blur(12px)",
  zIndex: 1300,
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  padding: "20px",
});

const PopupCard = styled(motion.div)({
  background:
    "linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%)",
  borderRadius: "32px",
  padding: "48px",
  width: "100%",
  maxWidth: "600px",
  color: "#fff",
  boxShadow: "0 32px 64px rgba(0, 0, 0, 0.4)",
  backdropFilter: "blur(24px)",
  border: "1px solid rgba(59, 130, 246, 0.3)",
  position: "relative",
});

const CloseButton = styled(IconButton)({
  position: "absolute",
  top: "16px",
  right: "16px",
  color: "#cbd5e1",
  background: "rgba(255, 255, 255, 0.1)",
  backdropFilter: "blur(10px)",
  "&:hover": {
    color: "#ffffff",
    background: "rgba(239, 68, 68, 0.8)",
    transform: "scale(1.1)",
  },
});

const FormField = styled(TextField)({
  marginBottom: "24px",
  "& label": {
    color: "#cbd5e1",
    fontWeight: 500,
  },
  "& label.Mui-focused": {
    color: "#3b82f6",
  },
  "& .MuiOutlinedInput-root": {
    color: "#f8fafc",
    background: "rgba(255, 255, 255, 0.05)",
    borderRadius: "12px",
    "& fieldset": {
      borderColor: "rgba(255, 255, 255, 0.2)",
    },
    "&:hover fieldset": {
      borderColor: "#3b82f6",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#3b82f6",
      borderWidth: "2px",
    },
  },
});

const SubmitButton = styled(Button)({
  background: "linear-gradient(135deg, #3b82f6 0%, #a855f7 100%)",
  color: "#fff",
  padding: "16px 32px",
  borderRadius: "12px",
  fontWeight: 700,
  fontSize: "1.1rem",
  textTransform: "none",
  boxShadow: "0 8px 24px rgba(59, 130, 246, 0.4)",
  transition: "all 0.3s ease",
  "&:hover": {
    background: "linear-gradient(135deg, #2563eb 0%, #9333ea 100%)",
    transform: "translateY(-2px)",
    boxShadow: "0 12px 32px rgba(59, 130, 246, 0.6)",
  },
  width: "100%",
});

function Footer() {
  const theme = useTheme();
  const { isDarkMode } = useThemeMode();
  const { t } = useLanguage();

  const [popupOpen, setPopupOpen] = useState(false);
  const [newsletterEmail, setNewsletterEmail] = useState("");
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Back to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // Show/hide back to top button based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const stats = [
    { number: "500+", label: "Clients Served", icon: <BusinessIcon /> },
    { number: "15+", label: "Years Experience", icon: <TrendingUpIcon /> },
    { number: "6", label: "Countries", icon: <PublicIcon /> },
    { number: "99%", label: "Client Satisfaction", icon: <StarIcon /> },
  ];

  const certifications = [
    { label: "ISO 9001:2015", icon: <VerifiedIcon /> },
    { label: "ACCA Approved", icon: <SecurityIcon /> },
    { label: "NBAA Member", icon: <VerifiedIcon /> },
    { label: "TRA Certified", icon: <SecurityIcon /> },
  ];

  const quickLinks = [
    { label: "TRA", href: "https://www.tra.go.tz", icon: <PublicIcon /> },
    { label: "BOT", href: "https://www.bot.go.tz", icon: <PublicIcon /> },
    { label: "NSSF", href: "https://www.nssf.go.tz", icon: <PublicIcon /> },
    { label: "PPF", href: "#", icon: <PublicIcon /> },
    {
      label: "Ministry of Finance",
      href: "https://www.mof.go.tz",
      icon: <PublicIcon />,
    },
    { label: "SSRA", href: "#", icon: <PublicIcon /> },
    { label: "WCF", href: "#", icon: <PublicIcon /> },
  ];

  const socialLinks = [
    { Icon: FacebookIcon, href: "#", label: "Facebook" },
    { Icon: TwitterIcon, href: "#", label: "Twitter" },
    { Icon: InstagramIcon, href: "#", label: "Instagram" },
    { Icon: LinkedInIcon, href: "#", label: "LinkedIn" },
    { Icon: YouTubeIcon, href: "#", label: "YouTube" },
  ];

  return (
    <>
      <FooterSection>
        <Container>
          {/* Stats Section */}
          <StatsGrid
            container
            spacing={{ xs: 2, sm: 2, md: 3, lg: 4, xl: 4 }}
            justifyContent="center"
          >
            {stats.map((stat, index) => (
              <Grid
                item
                xs={6}
                sm={6}
                md={3}
                lg={3}
                xl={3}
                key={index}
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  mb: { xs: 2, md: 0 },
                }}
              >
                <StatCard
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Box
                    sx={{
                      color: "#3b82f6",
                      mb: { xs: 1, md: 1.5, xl: 2 },
                      "& .MuiSvgIcon-root": {
                        fontSize: {
                          xs: "1.8rem",
                          md: "2.2rem",
                          lg: "2.5rem",
                          xl: "2.8rem",
                        },
                      },
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 800,
                      color: "#ffffff",
                      mb: 0.5,
                      fontSize: {
                        xs: "1.4rem",
                        md: "1.8rem",
                        lg: "2rem",
                        xl: "2.2rem",
                      },
                      lineHeight: 1.2,
                    }}
                  >
                    {stat.number}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#cbd5e1",
                      fontSize: {
                        xs: "0.8rem",
                        md: "0.9rem",
                        lg: "1rem",
                        xl: "1.1rem",
                      },
                      lineHeight: 1.3,
                      textAlign: "center",
                    }}
                  >
                    {stat.label}
                  </Typography>
                </StatCard>
              </Grid>
            ))}
          </StatsGrid>

          {/* Main Footer Content */}
          <MainFooterContent
            container
            spacing={{ xs: 3, sm: 4, md: 5, lg: 6, xl: 6 }}
          >
            {/* Company Info */}
            <Grid
              item
              xs={12}
              sm={12}
              md={6}
              lg={4}
              xl={4}
              sx={{ mb: { xs: 3, sm: 4, md: 0 } }}
            >
              <FooterCard
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                viewport={{ once: true }}
              >
                <SectionTitle>
                  <BusinessIcon />
                  Nelainey Consulting
                </SectionTitle>
                <Typography sx={{ color: "#cbd5e1", mb: 3, lineHeight: 1.7 }}>
                  Empowering businesses through reliable, tech-driven consulting
                  solutions across East Africa. Your trusted partner for
                  sustainable growth and innovation.
                </Typography>

                <ContactInfo>
                  <LocationOnIcon
                    sx={{
                      color: "#3b82f6",
                      fontSize: { xs: "1.2rem", md: "1.5rem" },
                    }}
                  />
                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        color: "#ffffff",
                        fontSize: { xs: "0.9rem", md: "1rem" },
                      }}
                    >
                      Head Office - Tanzania
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#cbd5e1",
                        fontSize: { xs: "0.8rem", md: "0.9rem" },
                      }}
                    >
                      Lumumba Complex, Mafia St., Dar es Salaam
                    </Typography>
                  </Box>
                </ContactInfo>

                <ContactInfo>
                  <PhoneIcon
                    sx={{
                      color: "#3b82f6",
                      fontSize: { xs: "1.2rem", md: "1.5rem" },
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#cbd5e1",
                      fontSize: { xs: "0.9rem", md: "1rem" },
                    }}
                  >
                    +255 22 123 4567
                  </Typography>
                </ContactInfo>

                <ContactInfo>
                  <EmailIcon
                    sx={{
                      color: "#3b82f6",
                      fontSize: { xs: "1.2rem", md: "1.5rem" },
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#cbd5e1",
                      fontSize: { xs: "0.9rem", md: "1rem" },
                    }}
                  >
                    <EMAIL>
                  </Typography>
                </ContactInfo>

                <Box sx={{ mt: 3, textAlign: "center" }}>
                  <Typography
                    variant="h6"
                    sx={{
                      color: "#ffffff",
                      mb: 2,
                      fontWeight: 600,
                      fontSize: { xs: "1.1rem", md: "1.25rem" },
                    }}
                  >
                    Certifications & Memberships
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      gap: { xs: 1, md: 1.5 },
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {certifications.map((cert, index) => (
                      <CertificationChip
                        key={index}
                        icon={cert.icon}
                        label={cert.label}
                        size="small"
                        sx={{
                          fontSize: { xs: "0.7rem", md: "0.8rem" },
                          height: { xs: "28px", md: "32px" },
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              </FooterCard>
            </Grid>

            {/* Quick Links */}
            <Grid
              item
              xs={12}
              sm={6}
              md={3}
              lg={3}
              xl={3}
              sx={{ mb: { xs: 3, sm: 4, md: 0 } }}
            >
              <FooterCard
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                viewport={{ once: true }}
                sx={{
                  [theme.breakpoints.down("sm")]: {
                    padding: "16px 12px", // Slightly reduced padding for mobile
                  },
                }}
              >
                <SectionTitle>
                  <PublicIcon />
                  Government Links
                </SectionTitle>
                <Box
                  sx={{
                    flex: 1,
                    display: "flex",
                    flexDirection: { xs: "row", sm: "column" }, // Horizontal on mobile, vertical on desktop
                    alignItems: "center",
                    justifyContent: { xs: "center", sm: "flex-start" }, // Center on mobile
                    gap: { xs: "12px", sm: "10px" }, // Horizontal spacing on mobile
                    flexWrap: { xs: "wrap", sm: "nowrap" }, // Allow wrapping on mobile
                    [theme.breakpoints.down("sm")]: {
                      gap: "8px", // Tighter spacing for mobile
                      maxWidth: "100%",
                    },
                  }}
                >
                  {quickLinks.map((link, index) => (
                    <LinkText
                      key={index}
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {link.icon}
                      {link.label}
                    </LinkText>
                  ))}
                </Box>
              </FooterCard>
            </Grid>

            {/* Services */}
            <Grid
              item
              xs={12}
              sm={6}
              md={3}
              lg={2}
              xl={2}
              sx={{ mb: { xs: 3, sm: 4, md: 0 } }}
            >
              <FooterCard
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                viewport={{ once: true }}
                sx={{
                  [theme.breakpoints.down("sm")]: {
                    padding: "16px 12px", // Slightly reduced padding for mobile
                  },
                }}
              >
                <SectionTitle>
                  <TrendingUpIcon />
                  Our Services
                </SectionTitle>
                <Box
                  sx={{
                    flex: 1,
                    display: "flex",
                    flexDirection: { xs: "row", sm: "column" }, // Horizontal on mobile, vertical on desktop
                    alignItems: "center",
                    justifyContent: { xs: "center", sm: "flex-start" }, // Center on mobile
                    gap: { xs: "12px", sm: "10px" }, // Horizontal spacing on mobile
                    flexWrap: { xs: "wrap", sm: "nowrap" }, // Allow wrapping on mobile
                    [theme.breakpoints.down("sm")]: {
                      gap: "8px", // Tighter spacing for mobile
                      maxWidth: "100%",
                    },
                  }}
                >
                  {[
                    "Business Advisory",
                    "Tax Compliance",
                    "Accounting",
                    "Assurance",
                    "HR Services",
                    "IT Consultancy",
                    "Corporate Training",
                  ].map((service, index) => (
                    <LinkText key={index} href="#services">
                      {service}
                    </LinkText>
                  ))}
                </Box>
              </FooterCard>
            </Grid>

            {/* Newsletter & Social */}
            <Grid
              item
              xs={12}
              sm={12}
              md={3}
              lg={3}
              xl={3}
              sx={{ mb: { xs: 3, sm: 4, md: 0 } }}
            >
              <FooterCard
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                viewport={{ once: true }}
              >
                <SectionTitle>
                  <SendIcon />
                  Stay Connected
                </SectionTitle>
                <Typography
                  sx={{
                    color: "#cbd5e1",
                    mb: 3,
                    lineHeight: 1.6,
                    fontSize: { xs: "0.9rem", md: "1rem", xl: "1.125rem" },
                  }}
                >
                  Subscribe to our newsletter for the latest insights and
                  updates.
                </Typography>

                <NewsletterCard>
                  <CardContent
                    sx={{
                      p: { xs: 2, md: 3, xl: 4 },
                      "&:last-child": { pb: { xs: 2, md: 3, xl: 4 } },
                    }}
                  >
                    <TextField
                      fullWidth
                      variant="outlined"
                      placeholder="Enter your email"
                      value={newsletterEmail}
                      onChange={(e) => setNewsletterEmail(e.target.value)}
                      sx={{
                        mb: 2,
                        "& .MuiOutlinedInput-root": {
                          background: "rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          color: "#ffffff",
                          fontSize: { xs: "0.9rem", xl: "1rem" },
                          "& fieldset": {
                            borderColor: "rgba(255, 255, 255, 0.2)",
                          },
                          "&:hover fieldset": {
                            borderColor: "#3b82f6",
                          },
                          "&.Mui-focused fieldset": {
                            borderColor: "#3b82f6",
                          },
                        },
                        "& .MuiInputBase-input::placeholder": {
                          color: "#cbd5e1",
                          opacity: 1,
                        },
                      }}
                    />
                    <Button
                      fullWidth
                      variant="contained"
                      endIcon={<SendIcon />}
                      sx={{
                        background:
                          "linear-gradient(135deg, #3b82f6 0%, #a855f7 100%)",
                        borderRadius: "12px",
                        py: { xs: 1.2, xl: 1.8 },
                        fontWeight: 600,
                        textTransform: "none",
                        fontSize: { xs: "0.9rem", xl: "1rem" },
                        "&:hover": {
                          background:
                            "linear-gradient(135deg, #2563eb 0%, #9333ea 100%)",
                        },
                      }}
                    >
                      Subscribe
                    </Button>
                  </CardContent>
                </NewsletterCard>

                <SocialIconsBox>
                  {socialLinks.map((social, index) => (
                    <Tooltip key={index} title={social.label} arrow>
                      <IconButtonStyled
                        component="a"
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label={social.label}
                      >
                        <social.Icon fontSize="large" />
                      </IconButtonStyled>
                    </Tooltip>
                  ))}
                </SocialIconsBox>

                <Button
                  variant="contained"
                  onClick={() => setPopupOpen(true)}
                  sx={{
                    mt: 3,
                    background:
                      "linear-gradient(135deg, #22c55e 0%, #3b82f6 100%)",
                    borderRadius: "12px",
                    padding: "12px 24px",
                    fontWeight: 700,
                    textTransform: "none",
                    width: "100%",
                    "&:hover": {
                      background:
                        "linear-gradient(135deg, #16a34a 0%, #2563eb 100%)",
                      transform: "translateY(-2px)",
                    },
                  }}
                >
                  Contact Us
                </Button>
              </FooterCard>
            </Grid>
          </MainFooterContent>

          {/* Sub Footer */}
          <SubFooter>
            <Grid
              container
              spacing={{ xs: 1, sm: 1.5, md: 2 }}
              alignItems="center"
              justifyContent="center" // Center the Grid container
              sx={{
                maxWidth: "100%",
                margin: "0 auto",
                width: "100%",
                px: 0, // Remove horizontal padding to avoid offset
              }}
            >
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  display: "flex",
                  justifyContent: "center", // Center content within Grid item
                  px: { xs: 0.5, sm: 0.75, md: 0 }, // Minimal padding for spacing
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "#94a3b8",
                    textAlign: "center",
                    fontSize: { xs: "0.7rem", sm: "0.75rem", md: "0.875rem" },
                    lineHeight: 1.5,
                    wordBreak: "break-word",
                    width: "100%", // Ensure full width for centering
                  }}
                >
                  © {new Date().getFullYear()} Nelainey Consulting. All rights
                  reserved.
                </Typography>
              </Grid>
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  display: "flex",
                  justifyContent: "center", // Center content within Grid item
                  px: { xs: 0.5, sm: 0.75, md: 0 },
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    gap: { xs: 1, sm: 1.5, md: 3 },
                    justifyContent: "center",
                    flexWrap: "wrap",
                    alignItems: "center",
                    width: "100%", // Ensure full width for centering
                  }}
                >
                  <Link
                    href="#"
                    sx={{
                      color: "#94a3b8",
                      textDecoration: "none",
                      fontSize: { xs: "0.65rem", sm: "0.7rem", md: "0.875rem" },
                      "&:hover": { color: "#3b82f6" },
                      transition: "color 0.3s ease",
                      whiteSpace: "nowrap",
                      padding: { xs: "4px 6px", sm: "4px 8px" },
                      textAlign: "center",
                    }}
                  >
                    Privacy Policy
                  </Link>
                  <Link
                    href="#"
                    sx={{
                      color: "#94a3b8",
                      textDecoration: "none",
                      fontSize: { xs: "0.65rem", sm: "0.7rem", md: "0.875rem" },
                      "&:hover": { color: "#3b82f6" },
                      transition: "color 0.3s ease",
                      whiteSpace: "nowrap",
                      padding: { xs: "4px 6px", sm: "4px 8px" },
                      textAlign: "center",
                    }}
                  >
                    Terms of Service
                  </Link>
                  <Link
                    href="#"
                    sx={{
                      color: "#94a3b8",
                      textDecoration: "none",
                      fontSize: { xs: "0.65rem", sm: "0.7rem", md: "0.875rem" },
                      "&:hover": { color: "#3b82f6" },
                      transition: "color 0.3s ease",
                      whiteSpace: "nowrap",
                      padding: { xs: "4px 6px", sm: "4px 8px" },
                      textAlign: "center",
                    }}
                  >
                    Cookie Policy
                  </Link>
                </Box>
              </Grid>
            </Grid>
          </SubFooter>
        </Container>
      </FooterSection>

      {/* Back to Top Button */}
      <AnimatePresence>
        {showBackToTop && (
          <BackToTopButton
            component={motion.div}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            onClick={scrollToTop}
            aria-label="Back to top"
          >
            <KeyboardArrowUpIcon fontSize="large" />
          </BackToTopButton>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {popupOpen && (
          <Overlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setPopupOpen(false)}
            aria-modal="true"
            role="dialog"
            tabIndex={-1}
          >
            <PopupCard
              initial={{ scale: 0.8, opacity: 0, rotateY: -15 }}
              animate={{ scale: 1, opacity: 1, rotateY: 0 }}
              exit={{ scale: 0.8, opacity: 0, rotateY: 15 }}
              onClick={(e) => e.stopPropagation()}
            >
              <CloseButton onClick={() => setPopupOpen(false)}>
                <CloseIcon />
              </CloseButton>

              <Box sx={{ textAlign: "center", mb: 4 }}>
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    margin: "0 auto 16px",
                    background:
                      "linear-gradient(135deg, #3b82f6 0%, #a855f7 100%)",
                  }}
                >
                  <ContactMailIcon sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography
                  variant="h4"
                  sx={{ mb: 2, fontWeight: 800, color: "#ffffff" }}
                >
                  Let's Connect
                </Typography>
                <Typography
                  variant="body1"
                  sx={{ color: "#cbd5e1", lineHeight: 1.6 }}
                >
                  Ready to transform your business? Get in touch with our expert
                  team for a personalized consultation.
                </Typography>
              </Box>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  alert(
                    "Thank you for your message! We'll get back to you within 24 hours."
                  );
                  setPopupOpen(false);
                }}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormField
                      fullWidth
                      required
                      label="First Name"
                      variant="outlined"
                      name="firstName"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormField
                      fullWidth
                      required
                      label="Last Name"
                      variant="outlined"
                      name="lastName"
                    />
                  </Grid>
                </Grid>

                <FormField
                  fullWidth
                  required
                  label="Email Address"
                  variant="outlined"
                  type="email"
                  name="email"
                />

                <FormField
                  fullWidth
                  label="Phone Number"
                  variant="outlined"
                  type="tel"
                  name="phone"
                />

                <FormField
                  fullWidth
                  label="Company Name"
                  variant="outlined"
                  name="company"
                />

                <FormField
                  fullWidth
                  select
                  label="Service Interest"
                  variant="outlined"
                  name="service"
                  slotProps={{ select: { native: true } }}
                >
                  <option value="">Select a service</option>
                  <option value="business-advisory">Business Advisory</option>
                  <option value="tax-compliance">Tax Compliance</option>
                  <option value="accounting">Accounting</option>
                  <option value="assurance">Assurance</option>
                  <option value="hr-services">HR Services</option>
                  <option value="it-consultancy">IT Consultancy</option>
                  <option value="corporate-training">Corporate Training</option>
                </FormField>

                <FormField
                  fullWidth
                  multiline
                  rows={4}
                  label="Tell us about your project or requirements"
                  variant="outlined"
                  name="message"
                />

                <SubmitButton type="submit" startIcon={<SendIcon />}>
                  Send Message
                </SubmitButton>
              </form>
            </PopupCard>
          </Overlay>
        )}
      </AnimatePresence>
    </>
  );
}

export default Footer;
