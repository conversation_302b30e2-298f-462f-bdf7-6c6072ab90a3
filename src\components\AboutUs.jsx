import React, { useRef } from 'react';
import { Box, Typography, styled, Container } from '@mui/material';
import { motion, useInView } from 'framer-motion';
import { AutoAwesome, TrendingUp, Business, Security } from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';

const Section = styled(Box)(({ theme }) => ({
  marginTop: '80px',
  padding: '80px 0',
  background: `
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  overflow: 'hidden',
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('md')]: {
    padding: '60px 0',
    marginTop: '60px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 0',
    marginTop: '40px',
  },
}));



const Content = styled(Box)(({ theme }) => ({
  maxWidth: '1400px',
  margin: '0 auto',
  color: '#f8fafc',
  fontFamily: "'Inter', 'Roboto', sans-serif",
  background: `
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.05) 100%
    )
  `,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(25px)',
  WebkitBackdropFilter: 'blur(25px)',
  borderRadius: '32px',
  padding: '80px 60px',
  boxShadow: `
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('lg')]: {
    padding: '60px 40px',
    maxWidth: '1200px',
  },
  [theme.breakpoints.down('md')]: {
    padding: '50px 30px',
    borderRadius: '24px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 20px',
    borderRadius: '20px',
    margin: '0 16px',
  },
}));

// Removed unused Highlight component

const StatsGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
  gap: '24px',
  marginTop: '40px',
  marginBottom: '40px',
  [theme.breakpoints.down('sm')]: {
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '16px',
  },
}));

const StatCard = styled(motion.div)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '16px',
  padding: '24px 20px',
  textAlign: 'center',
  transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  '&:hover': {
    transform: 'translateY(-4px)',
    background: 'rgba(255, 255, 255, 0.15)',
    boxShadow: '0 12px 40px rgba(59, 130, 246, 0.2)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '20px 16px',
  },
}));

const AboutUsSection = () => {
  const { t } = useLanguage();

  const sectionRef = useRef(null);
  const sectionInView = useInView(sectionRef, { once: true, amount: 0.1 });

  // Enhanced animation variants
  const textVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
        staggerChildren: 0.1
      }
    },
  };

  const statsData = [
    { number: '500+', label: t('about.stats.clients'), icon: <Business /> },
    { number: '13+', label: t('about.stats.experience'), icon: <TrendingUp /> },
    { number: '100%', label: t('about.stats.satisfaction'), icon: <Security /> },
    { number: '24/7', label: t('about.stats.support'), icon: <AutoAwesome /> },
  ];

  return (
    <Section id="about" ref={sectionRef}>
      <Container maxWidth="xl">
        <motion.div
          initial="hidden"
          animate={sectionInView ? "visible" : "hidden"}
          variants={textVariants}
        >
          <Content>
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 50 }}
              animate={sectionInView ? { opacity: 1, scale: 1, y: 0 } : {}}
              transition={{ duration: 1, ease: [0.175, 0.885, 0.32, 1.275] }}
              style={{ textAlign: 'center', marginBottom: '40px' }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                <AutoAwesome sx={{ fontSize: '2.5rem', color: '#3b82f6', mr: 2 }} />
                <Typography
                  variant="h2"
                  sx={{
                    fontWeight: '900',
                    background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                    fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                    letterSpacing: '-0.02em',
                    fontFamily: "'Inter', 'Montserrat', sans-serif"
                  }}
                >
                  {t('about.title')}
                </Typography>
                <Business sx={{ fontSize: '2.5rem', color: '#10b981', ml: 2 }} />
              </Box>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={sectionInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Typography
                variant="h5"
                sx={{
                  fontWeight: '400',
                  color: '#cbd5e1',
                  lineHeight: 1.7,
                  fontSize: { xs: '1.2rem', md: '1.4rem' },
                  textAlign: 'center',
                  mb: 4,
                  position: 'relative',
                  zIndex: 1
                }}
              >
                {t('about.subtitle')}
              </Typography>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={sectionInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <StatsGrid>
                {statsData.map((stat, index) => (
                  <StatCard
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={sectionInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                    whileHover={{ scale: 1.05 }}
                  >
                    <Box sx={{ color: '#3b82f6', mb: 1 }}>
                      {stat.icon}
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: '800', color: '#f8fafc', mb: 1 }}>
                      {stat.number}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#94a3b8', fontWeight: '500' }}>
                      {stat.label}
                    </Typography>
                  </StatCard>
                ))}
              </StatsGrid>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={sectionInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <Typography
                variant="body1"
                sx={{
                  color: '#94a3b8',
                  fontSize: { xs: '1rem', md: '1.1rem' },
                  lineHeight: 1.8,
                  textAlign: 'center',
                  position: 'relative',
                  zIndex: 1
                }}
              >
                {t('about.description')}
              </Typography>
            </motion.div>
          </Content>
        </motion.div>
      </Container>
    </Section>
  );
};

export default AboutUsSection;