import React, { useState, useEffect, useRef, useCallback } from "react";
import { Box, Typography, Container } from "@mui/material";
import { styled } from "@mui/system";
import {
  People,
  DoneAll,
  CalendarToday,
  Schedule,
  AutoAwesome,
  TrendingUp
} from "@mui/icons-material";
import { motion, AnimatePresence, useInView } from "framer-motion";
import { useTheme, useMediaQuery } from "@mui/material";
import { useLanguage } from "../contexts/LanguageContext";

const createStats = (t) => [
  { icon: People, count: 500, title: t('stats.clients'), color: "#3b82f6" },
  { icon: DoneAll, count: 1200, title: t('stats.projects'), color: "#10b981" },
  { icon: CalendarToday, count: 13, title: t('stats.experience'), color: "#f59e0b" },
  { icon: Schedule, count: 24, title: t('stats.support'), color: "#ef4444" },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: '80px',
  padding: '80px 0',
  background: `
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  overflow: 'hidden',
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('md')]: {
    padding: '60px 0',
    marginTop: '60px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 0',
    marginTop: '40px',
  },
}));

const StatsGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: '32px',
  marginTop: '60px',
  padding: '0 20px',
  gridTemplateColumns: 'repeat(4, 1fr)', // Fixed 4 columns for desktop
  width: '100%',
  maxWidth: '1400px',
  marginLeft: 'auto',
  marginRight: 'auto',
  position: 'relative',
  justifyItems: 'center',
  [theme.breakpoints.up('xl')]: {
    gridTemplateColumns: 'repeat(4, 1fr)', // Keep 4 columns on extra large screens
    gap: '40px',
    maxWidth: '1600px',
  },
  [theme.breakpoints.down('lg')]: {
    gridTemplateColumns: 'repeat(2, 1fr)', // 2 columns for large tablets
    gap: '28px',
  },
  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(2, 1fr)', // 2 columns for tablets
    gap: '24px',
    padding: '0 16px',
  },
  [theme.breakpoints.down('sm')]: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '20px',
    padding: '0 12px',
    overflow: 'visible',
  },
}));

const StatsCard = styled(motion.div)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  maxWidth: '320px',
  height: '280px',
  borderRadius: '24px',
  background: `
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.05) 100%
    )
  `,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(20px)',
  WebkitBackdropFilter: 'blur(20px)',
  boxShadow: `
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  textAlign: 'center',
  padding: '32px 24px',
  transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  willChange: 'transform, box-shadow',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
    pointerEvents: 'none',
  },
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: `
      0 35px 70px -12px rgba(0, 0, 0, 0.35),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 50px rgba(59, 130, 246, 0.3)
    `,
  },
  '&:active': {
    transform: 'translateY(-4px) scale(0.98)',
  },
  [theme.breakpoints.down('lg')]: {
    maxWidth: '300px',
    height: '260px',
  },
  [theme.breakpoints.down('md')]: {
    maxWidth: '280px',
    height: '240px',
  },
  [theme.breakpoints.down('sm')]: {
    maxWidth: '320px',
    height: '220px',
    borderRadius: '20px',
    padding: '28px 20px',
  },
}));

const Stats = () => {
  const { t } = useLanguage();
  const STATS = createStats(t);

  const [displayCounts, setDisplayCounts] = useState(STATS.map(() => 0));
  const [currentIndex, setCurrentIndex] = useState(0);
  const animationFrame = useRef(null);
  const sectionRef = useRef();
  const gridRef = useRef();
  const hasAnimated = useRef(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [isPaused, setIsPaused] = useState(false);

  const sectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isInView = useInView(gridRef, { once: false, amount: 0.2 });

  const animateCounts = useCallback(() => {
    cancelAnimationFrame(animationFrame.current);
    const targets = STATS.map((stat) => stat.count);

    animationFrame.current = requestAnimationFrame(function step() {
      setDisplayCounts((current) => {
        let done = true;
        const next = current.map((val, idx) => {
          const target = targets[idx];
          if (val < target) {
            done = false;
            return Math.min(val + Math.ceil(target * 0.05), target);
          }
          return target;
        });
        if (!done) animationFrame.current = requestAnimationFrame(step);
        return next;
      });
    });
  }, [STATS]);

  useEffect(() => {
    if (sectionInView && !hasAnimated.current) {
      animateCounts();
      hasAnimated.current = true;
    }
  }, [sectionInView, animateCounts]);

  useEffect(() => {
    let interval;
    if (!isPaused && isMobile) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % STATS.length);
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isPaused, isMobile, STATS.length]);

  // Enhanced animation variants
  const textVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
        staggerChildren: 0.1
      }
    },
  };

  const slideVariants = {
    enter: { x: 300, opacity: 0, scale: 0.9 },
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
    exit: {
      x: -300,
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.6,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
  };

  // Stagger animation for grid items
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9,
      rotateX: 15
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
  };

  return (
    <Section ref={sectionRef} id="stats">
      <Container maxWidth="xl">
        <motion.div
          initial="hidden"
          animate={sectionInView ? "visible" : "hidden"}
          variants={textVariants}
          style={{ textAlign: 'center', marginBottom: '40px' }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={sectionInView ? { opacity: 1, scale: 1, y: 0 } : {}}
            transition={{ duration: 1, ease: [0.175, 0.885, 0.32, 1.275] }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <AutoAwesome sx={{ fontSize: '2rem', color: '#3b82f6', mr: 2 }} />
              <Typography
                variant="h2"
                sx={{
                  fontWeight: '900',
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                  fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                  letterSpacing: '-0.02em',
                }}
              >
                Numbers That Reflect Our Impact
              </Typography>
              <TrendingUp sx={{ fontSize: '2rem', color: '#10b981', ml: 2 }} />
            </Box>
            <Typography
              variant="h5"
              sx={{
                maxWidth: 800,
                mx: 'auto',
                color: '#cbd5e1',
                fontWeight: '400',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                opacity: 0.9
              }}
            >
              At Nelainey Consulting, we transform numbers into milestones.
              From hundreds of clients served to years of expertise—our track
              record speaks for itself. Here’s how we make a measurable difference.
            </Typography>
          </motion.div>
        </motion.div>

        <motion.div
          ref={gridRef}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <StatsGrid>
            {!isMobile ? (
              STATS.map((stat, idx) => (
                <motion.div
                  key={idx}
                  variants={itemVariants}
                  style={{
                    position: 'relative',
                    width: '100%',
                    willChange: 'transform, opacity'
                  }}
                >
                  <StatsCard
                    whileHover={{
                      scale: 1.02,
                      transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] }
                    }}
                  >
                    <Box sx={{ position: 'relative', zIndex: 1 }}>
                      <Box sx={{ color: stat.color, mb: 2 }}>
                        <stat.icon sx={{ fontSize: { xs: '3rem', md: '4rem' } }} />
                      </Box>
                      <Typography
                        component="span"
                        sx={{
                          fontWeight: 900,
                          fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem', lg: '4rem' },
                          color: "#f8fafc",
                          fontFamily: "'Inter', 'Montserrat', sans-serif",
                          fontVariantNumeric: "tabular-nums",
                          letterSpacing: "-0.02em",
                          display: "block",
                          mb: 1,
                          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                          backgroundClip: 'text',
                        }}
                      >
                        {displayCounts[idx].toLocaleString()}+
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: "#cbd5e1",
                          fontWeight: 600,
                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                          fontFamily: "'Inter', 'Roboto', sans-serif",
                          textAlign: 'center',
                        }}
                      >
                        {stat.title}
                      </Typography>
                    </Box>
                  </StatsCard>
                </motion.div>
              ))
            ) : (
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                custom={0}
                initial="enter"
                animate="center"
                exit="exit"
                variants={slideVariants}
                transition={{ duration: 0.9, ease: [0.25, 0.1, 0.25, 1] }}
                style={{ position: "relative", width: "100%" }}
              >
                <StatsCard
                  onClick={() => setIsPaused(!isPaused)}
                  whileHover={{ scale: 1.05, transition: { duration: 0.3 } }}
                >
                  <Box sx={{ color: STATS[currentIndex].color, mb: 2 }}>
                    {React.createElement(STATS[currentIndex].icon, { sx: { fontSize: '3rem' } })}
                  </Box>
                  <Typography
                    component="span"
                    sx={{
                      fontWeight: 800,
                      fontSize: 28,
                      color: "#0d1136",
                      fontFamily: "'Montserrat', sans-serif",
                      fontVariantNumeric: "tabular-nums",
                      letterSpacing: "0.02em",
                      display: "block",
                    }}
                  >
                    {displayCounts[currentIndex].toLocaleString()}+
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#555",
                      fontWeight: 600,
                      fontSize: 13,
                      fontFamily: "'Roboto', sans-serif",
                      marginTop: 6,
                    }}
                  >
                    {STATS[currentIndex].title}
                  </Typography>
                </StatsCard>
              </motion.div>
            </AnimatePresence>
          )}
          </StatsGrid>
        </motion.div>
      </Container>
    </Section>
  );
};

export default Stats;